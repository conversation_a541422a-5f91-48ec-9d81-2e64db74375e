package com.zsmall.activity.controller.controller;

import cn.hutool.core.date.DateUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.zsmall.activity.biz.util.TtlMessageValidator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * RabbitMQ诊断控制器
 * 用于诊断TTL+死信队列配置问题
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@RestController
@RequestMapping("/rabbitMqDiagnostic")
@RequiredArgsConstructor
@Tag(name = "RabbitMQ诊断", description = "RabbitMQ TTL+死信队列诊断接口")
public class RabbitMqDiagnosticController {
    
    @Resource
    private RabbitTemplate rabbitTemplate;
    
    @Resource
    private RabbitAdmin rabbitAdmin;
    
    @Resource
    private TtlMessageValidator ttlMessageValidator;
    
    /**
     * 验证TTL+死信队列机制
     */
    @PostMapping("/validateTtlMechanism")
    @Operation(summary = "验证TTL+死信队列机制")
    public R<Map<String, Object>> validateTtlMechanism(@RequestParam(defaultValue = "5000") long ttlMs) {
        try {
            Map<String, Object> result = ttlMessageValidator.validateTtlDeadLetterMechanism(ttlMs);
            return R.ok(result);
        } catch (Exception e) {
            log.error("验证TTL机制失败", e);
            return R.fail("验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取TTL测试结果
     */
    @GetMapping("/getTtlTestResults")
    @Operation(summary = "获取TTL测试结果")
    public R<Map<String, Object>> getTtlTestResults() {
        try {
            Map<String, Object> results = ttlMessageValidator.getTestResults();
            return R.ok(results);
        } catch (Exception e) {
            log.error("获取TTL测试结果失败", e);
            return R.fail("获取结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理TTL测试记录
     */
    @PostMapping("/clearTtlTestRecords")
    @Operation(summary = "清理TTL测试记录")
    public R<String> clearTtlTestRecords() {
        try {
            ttlMessageValidator.clearTestRecords();
            return R.ok("测试记录已清理");
        } catch (Exception e) {
            log.error("清理TTL测试记录失败", e);
            return R.fail("清理失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查队列状态
     */
    @GetMapping("/checkQueueStatus")
    @Operation(summary = "检查队列状态")
    public R<Map<String, Object>> checkQueueStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 检查TTL队列
            Properties ttlProps = rabbitAdmin.getQueueProperties(RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_TTL_QUEUE);
            if (ttlProps != null) {
                Map<String, Object> ttlStatus = new HashMap<>();
                ttlStatus.put("存在", true);
                ttlStatus.put("消息数", ttlProps.get("QUEUE_MESSAGE_COUNT"));
                ttlStatus.put("消费者数", ttlProps.get("QUEUE_CONSUMER_COUNT"));
                
                // 检查是否有消费者（这是问题的关键）
                Object consumerCount = ttlProps.get("QUEUE_CONSUMER_COUNT");
                if (consumerCount != null && Integer.parseInt(consumerCount.toString()) > 0) {
                    ttlStatus.put("警告", "TTL队列有消费者！这会导致消息被消费而不是等待过期转发到死信队列");
                    ttlStatus.put("解决方案", "确保没有消费者监听TTL队列，消息应该只通过TTL过期转发");
                }
                
                status.put("TTL队列", ttlStatus);
            } else {
                status.put("TTL队列", Map.of("存在", false, "错误", "TTL队列不存在"));
            }
            
            // 检查处理队列
            Properties processProps = rabbitAdmin.getQueueProperties(RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_PROCESS_QUEUE);
            if (processProps != null) {
                Map<String, Object> processStatus = new HashMap<>();
                processStatus.put("存在", true);
                processStatus.put("消息数", processProps.get("QUEUE_MESSAGE_COUNT"));
                processStatus.put("消费者数", processProps.get("QUEUE_CONSUMER_COUNT"));
                
                status.put("处理队列", processStatus);
            } else {
                status.put("处理队列", Map.of("存在", false, "错误", "处理队列不存在"));
            }
            
            status.put("检查时间", DateUtil.now());
            
            return R.ok(status);
            
        } catch (Exception e) {
            log.error("检查队列状态失败", e);
            return R.fail("检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送测试消息到TTL队列
     */
    @PostMapping("/sendTestMessage")
    @Operation(summary = "发送测试消息到TTL队列")
    public R<String> sendTestMessage(@RequestParam(defaultValue = "3000") long ttlMs) {
        try {
            String testMessage = "MANUAL_TEST_" + System.currentTimeMillis();
            
            rabbitTemplate.convertAndSend(
                RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_EXCHANGE,
                RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_ROUTING_KEY,
                testMessage,
                message -> {
                    message.getMessageProperties().setExpiration(String.valueOf(ttlMs));
                    return message;
                }
            );
            
            return R.ok("测试消息发送成功: " + testMessage + ", TTL: " + ttlMs + "ms");
            
        } catch (Exception e) {
            log.error("发送测试消息失败", e);
            return R.fail("发送失败: " + e.getMessage());
        }
    }
}
