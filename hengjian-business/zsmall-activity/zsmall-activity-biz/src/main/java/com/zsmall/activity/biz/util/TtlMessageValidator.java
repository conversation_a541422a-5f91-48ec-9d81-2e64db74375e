package com.zsmall.activity.biz.util;

import cn.hutool.core.date.DateUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * TTL消息验证工具
 * 用于验证TTL+死信队列机制是否正常工作
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@Component
public class TtlMessageValidator {
    
    @Resource
    private RabbitTemplate rabbitTemplate;
    
    @Resource
    private RabbitAdmin rabbitAdmin;
    
    /**
     * 测试消息记录
     */
    private static final ConcurrentHashMap<String, TestMessageRecord> TEST_MESSAGES = new ConcurrentHashMap<>();
    
    /**
     * 验证TTL+死信队列机制
     * 
     * @param ttlMs TTL时间（毫秒）
     * @return 验证结果
     */
    public Map<String, Object> validateTtlDeadLetterMechanism(long ttlMs) {
        Map<String, Object> result = new HashMap<>();
        String testMessageId = "TTL_TEST_" + System.currentTimeMillis();
        
        try {
            // 1. 检查队列状态
            Map<String, Object> queueStatus = checkQueueStatus();
            result.put("队列状态检查", queueStatus);
            
            // 2. 发送测试消息
            long sendTime = System.currentTimeMillis();
            sendTestMessage(testMessageId, ttlMs);
            
            // 3. 记录测试消息
            TestMessageRecord record = new TestMessageRecord();
            record.messageId = testMessageId;
            record.sendTime = sendTime;
            record.ttlMs = ttlMs;
            record.expectedArrivalTime = sendTime + ttlMs;
            TEST_MESSAGES.put(testMessageId, record);
            
            result.put("测试消息ID", testMessageId);
            result.put("发送时间", DateUtil.formatDateTime(new Date(sendTime)));
            result.put("TTL时间(ms)", ttlMs);
            result.put("预期到达时间", DateUtil.formatDateTime(new Date(record.expectedArrivalTime)));
            result.put("发送状态", "成功");
            
            log.info("TTL测试消息发送成功: ID={}, TTL={}ms", testMessageId, ttlMs);
            
        } catch (Exception e) {
            result.put("发送状态", "失败");
            result.put("错误信息", e.getMessage());
            log.error("TTL测试消息发送失败", e);
        }
        
        return result;
    }
    
    /**
     * 检查队列状态
     */
    private Map<String, Object> checkQueueStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            // 检查TTL队列
            Properties ttlProps = rabbitAdmin.getQueueProperties(RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_TTL_QUEUE);
            if (ttlProps != null) {
                status.put("TTL队列存在", true);
                status.put("TTL队列消息数", ttlProps.get("QUEUE_MESSAGE_COUNT"));
                status.put("TTL队列消费者数", ttlProps.get("QUEUE_CONSUMER_COUNT"));
                
                // 警告：TTL队列不应该有消费者
                Object consumerCount = ttlProps.get("QUEUE_CONSUMER_COUNT");
                if (consumerCount != null && Integer.parseInt(consumerCount.toString()) > 0) {
                    status.put("TTL队列警告", "TTL队列有消费者，这会导致消息被消费而不是等待过期！");
                }
            } else {
                status.put("TTL队列存在", false);
            }
            
            // 检查处理队列
            Properties processProps = rabbitAdmin.getQueueProperties(RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_PROCESS_QUEUE);
            if (processProps != null) {
                status.put("处理队列存在", true);
                status.put("处理队列消息数", processProps.get("QUEUE_MESSAGE_COUNT"));
                status.put("处理队列消费者数", processProps.get("QUEUE_CONSUMER_COUNT"));
            } else {
                status.put("处理队列存在", false);
            }
            
        } catch (Exception e) {
            status.put("检查异常", e.getMessage());
        }
        
        return status;
    }
    
    /**
     * 发送测试消息
     */
    private void sendTestMessage(String messageId, long ttlMs) {
        rabbitTemplate.convertAndSend(
            RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_EXCHANGE,
            RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_ROUTING_KEY,
            messageId,
            message -> {
                // 设置消息TTL
                message.getMessageProperties().setExpiration(String.valueOf(ttlMs));
                // 设置消息持久化
                message.getMessageProperties().setDeliveryMode(MessageProperties.DeliveryMode.PERSISTENT);
                // 添加时间戳
                message.getMessageProperties().setTimestamp(new Date());
                // 添加消息ID
                message.getMessageProperties().setMessageId(messageId);
                return message;
            }
        );
    }
    
    /**
     * 记录测试消息被消费
     */
    public static void recordTestMessageConsumed(String messageId) {
        TestMessageRecord record = TEST_MESSAGES.get(messageId);
        if (record != null) {
            record.consumedTime = System.currentTimeMillis();
            record.actualDelay = record.consumedTime - record.sendTime;
            record.delayDifference = record.actualDelay - record.ttlMs;
            
            log.info("TTL测试消息被消费: ID={}, 预期延迟={}ms, 实际延迟={}ms, 差异={}ms", 
                messageId, record.ttlMs, record.actualDelay, record.delayDifference);
        }
    }
    
    /**
     * 获取测试结果
     */
    public Map<String, Object> getTestResults() {
        Map<String, Object> results = new HashMap<>();
        
        TEST_MESSAGES.forEach((messageId, record) -> {
            Map<String, Object> recordMap = new HashMap<>();
            recordMap.put("发送时间", DateUtil.formatDateTime(new Date(record.sendTime)));
            recordMap.put("TTL时间(ms)", record.ttlMs);
            recordMap.put("预期到达时间", DateUtil.formatDateTime(new Date(record.expectedArrivalTime)));
            
            if (record.consumedTime > 0) {
                recordMap.put("实际消费时间", DateUtil.formatDateTime(new Date(record.consumedTime)));
                recordMap.put("实际延迟(ms)", record.actualDelay);
                recordMap.put("延迟差异(ms)", record.delayDifference);
                recordMap.put("状态", "已消费");
                
                if (Math.abs(record.delayDifference) > 5000) {
                    recordMap.put("警告", "延迟差异超过5秒");
                }
            } else {
                long currentTime = System.currentTimeMillis();
                if (currentTime > record.expectedArrivalTime + 10000) {
                    recordMap.put("状态", "超时未消费");
                    recordMap.put("警告", "消息可能丢失或TTL机制异常");
                } else {
                    recordMap.put("状态", "等待中");
                }
            }
            
            results.put(messageId, recordMap);
        });
        
        return results;
    }
    
    /**
     * 清理测试记录
     */
    public void clearTestRecords() {
        TEST_MESSAGES.clear();
        log.info("TTL测试记录已清理");
    }
    
    /**
     * 测试消息记录
     */
    private static class TestMessageRecord {
        String messageId;
        long sendTime;
        long ttlMs;
        long expectedArrivalTime;
        long consumedTime;
        long actualDelay;
        long delayDifference;
    }
}
